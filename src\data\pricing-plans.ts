export interface PricingFeature {
  text: string;
  included: boolean;
}

export interface PricingPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  period: string;
  isCurrentPlan?: boolean;
  isPopular?: boolean;
  features: PricingFeature[];
}

export const pricingPlans: PricingPlan[] = [
  {
    id: "free",
    name: "Free",
    price: 0,
    currency: "€",
    period: "per month",
    isCurrentPlan: true,
    features: [
      { text: "50 credits / day", included: true },
      { text: "1,000 credits / month", included: true },
      { text: "1 keyword OR 1 platform filters", included: true },
      { text: "Access to leads", included: false },
      { text: "Access to premium leads (score ≥ 5)", included: false },
      { text: "Auto enrichment (unless cost is ≥0)", included: false },
    ],
  },
  {
    id: "standard",
    name: "Standard",
    price: 7,
    currency: "€",
    period: "per month",
    features: [
      { text: "200 credits / day", included: true },
      { text: "5,000 credits / month", included: true },
      { text: "Up to 5 combined filters", included: true },
      { text: "Access to phone numbers", included: true },
      { text: "Partial Access to premium leads (score ≥ 5)", included: true },
      { text: "Auto enrichment", included: true },
    ],
  },
  {
    id: "premium",
    name: "Premium",
    price: 17,
    currency: "€",
    period: "per month",
    isPopular: true,
    features: [
      { text: "400 credits / day", included: true },
      { text: "12,000 credits / month", included: true },
      { text: "Unlimited filters", included: true },
      { text: "Filterable", included: true },
      { text: "Full Access to premium leads (score ≥ 5)", included: true },
      { text: "Auto enrichment with priority", included: true },
    ],
  },
];

export const creditResetPolicy = {
  freePlan: "Reset automatically on the 1st of each month.",
  paidPlans: "Automatically refilled via Stripe webhook.",
};
