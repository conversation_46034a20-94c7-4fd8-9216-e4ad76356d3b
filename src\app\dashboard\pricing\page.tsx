"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Check } from "lucide-react";
import { PricingGrid } from "@/components/pricing";
import React from "react";
import CreditPolicy from "@/components/pricing/CreditPolicy";

const Pricing = () => {
  return (
    <div className="flex-1 h-full Support">
      <Card className="h-full glassColor relative p-6">
        <div className="max-w-4xl mx-auto space-y-6">
          <Card className="glassColor ">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">
                    Your Current Plan :
                  </span>
                  <span className="font-semibold">Free</span>
                  <Badge
                    variant="secondary"
                    className="bg-green-100 text-green-700 hover:bg-green-100"
                  >
                    Active
                  </Badge>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    className="text-red-600 border-red-200 hover:bg-red-50 bg-transparent"
                  >
                    Cancel Subscription
                  </Button>
                  <Button className="bg-teal-600 hover:bg-teal-700">
                    Upgrade Plan
                  </Button>
                </div>
              </div>
              <p className="text-xs text-gray-500">
                Renews on July 1 2025, €0/ per month
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                {/* Usage Metrics */}
                <div className="space-y-4">
                  <div className="text-center">
                    <h3 className="text-sm font-medium text-gray-600 mb-2">
                      Credits per month
                    </h3>
                    <div className="relative w-16 h-16 mx-auto">
                      <svg
                        className="w-16 h-16 transform -rotate-90"
                        viewBox="0 0 36 36"
                      >
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#e5e7eb"
                          strokeWidth="2"
                        />
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#10b981"
                          strokeWidth="2"
                          strokeDasharray="25, 100"
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-sm font-semibold">25%</span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">250/1000</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="text-center">
                    <h3 className="text-sm font-medium text-gray-600 mb-2">
                      Daily Limit
                    </h3>
                    <div className="relative w-16 h-16 mx-auto">
                      <svg
                        className="w-16 h-16 transform -rotate-90"
                        viewBox="0 0 36 36"
                      >
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#e5e7eb"
                          strokeWidth="2"
                        />
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#10b981"
                          strokeWidth="2"
                          strokeDasharray="50, 100"
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-sm font-semibold">50%</span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">25/50</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="text-center">
                    <h3 className="text-sm font-medium text-gray-600 mb-2">
                      Auto Enrichment limit
                    </h3>
                    <div className="relative w-16 h-16 mx-auto">
                      <svg
                        className="w-16 h-16 transform -rotate-90"
                        viewBox="0 0 36 36"
                      >
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#e5e7eb"
                          strokeWidth="2"
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-sm font-semibold">0%</span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">0/0</p>
                  </div>
                </div>

                {/* Plan Features */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-600">
                      Plan Features
                    </h3>
                    <Button
                      variant="link"
                      className="text-teal-600 text-xs p-0 h-auto"
                    >
                      View more →
                    </Button>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-xs">
                      <Check className="w-3 h-3 text-green-600" />
                      <span>50 credits / day</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs">
                      <Check className="w-3 h-3 text-green-600" />
                      <span>1,000 credits / month</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs">
                      <Check className="w-3 h-3 text-green-600" />
                      <span>1 keyword OR 1 platform filters</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <PricingGrid
            onUpgrade={(planId) => {
              console.log("Upgrading to plan:", planId);
            }}
          />

          <CreditPolicy />
        </div>
      </Card>
    </div>
  );
};

export default Pricing;
