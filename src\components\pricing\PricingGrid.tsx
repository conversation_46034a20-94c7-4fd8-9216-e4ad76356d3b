"use client";

import { pricingPlans } from "@/data/pricing-plans";
import { PricingCard } from "./PricingCard";

interface PricingGridProps {
  onUpgrade?: (planId: string) => void;
  showCreditPolicy?: boolean;
}

export const PricingGrid = ({ onUpgrade }: PricingGridProps) => {
  return (
    <div className="space-y-6">
      {/* Explore Other Plans */}
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold">Explore Other plans</h2>
      </div>

      {/* Pricing Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {pricingPlans.map((plan) => (
          <PricingCard key={plan.id} plan={plan} onUpgrade={onUpgrade} />
        ))}
      </div>
    </div>
  );
};
